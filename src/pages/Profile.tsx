import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useBetaAccess } from '@/hooks/useBetaAccess';
import { useSubscription } from '@/hooks/useSubscription';
import { subscriptionService } from '@/services/subscriptionService';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { useNavigate } from 'react-router-dom';
import Header from '@/components/Header';

import { Badge } from '@/components/ui/badge';
import {
  CheckCircle,
  AlertCircle,
  ExternalLink,
  Star,
  CreditCard,
  Zap,
  Crown,
  Edit,
  LogOut,
  Bell,
  BellRing,
  TrendingUp,
  Eye,
  Activity,
  Shield,
  Lock,
  Settings,
  Calendar,
  Mail
} from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { getEmailProviderUrl } from '@/lib/emailUtils';
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose
} from '@/components/ui/dialog';

const Profile: React.FC = () => {
  const { user, signOut, isEmailVerified, sendVerificationEmail } = useAuth();
  const { hasBetaAccess } = useBetaAccess();
  const { subscription, planInfo, credits, loading, canUpgrade, isProUser, refetch } = useSubscription();
  const navigate = useNavigate();

  const [showUpgradeModal, setShowUpgradeModal] = React.useState(false);
  const [upgrading, setUpgrading] = React.useState(false);

  const handleSignOut = async () => {
    await signOut();
    navigate('/');
  };

  const handleUpgradeClick = () => {
    setShowUpgradeModal(true);
  };

  const handleSubscribeToPro = async () => {
    if (!user?.email) return;

    setUpgrading(true);
    try {
      // Get current domain for return URL (works with ngrok and localhost)
      const currentOrigin = window.location.origin;
      const returnUrl = `${currentOrigin}/profile?upgrade=success`;

      const response = await subscriptionService.createSubscription({
        plan_type: 'pro',
        customer_data: {
          name: user.user_metadata?.full_name || user.email,
          email: user.email,
        },
        return_url: returnUrl,
      });

      if (response.success && response.payment_link) {
        // Redirect to DodoPayments checkout
        window.location.href = response.payment_link;
      } else {
        console.error('Subscription creation failed:', response.error);
        alert('Failed to create subscription. Please try again.');
      }
    } catch (error) {
      console.error('Error creating subscription:', error);
      alert('An error occurred. Please try again.');
    } finally {
      setUpgrading(false);
      setShowUpgradeModal(false);
    }
  };

  const handleSendVerificationEmail = async () => {
    if (user?.email) {
      await sendVerificationEmail(user.email);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long'
    });
  };

  const getUserInitials = (email: string) => {
    return email.charAt(0).toUpperCase() + (email.split('@')[0].charAt(1) || '').toUpperCase();
  };

  const getUserDisplayName = (email: string) => {
    const name = email.split('@')[0];
    return name.charAt(0).toUpperCase() + name.slice(1).replace(/[._]/g, ' ');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header with gradient background */}
      <div className="bg-gradient-to-r from-pink-500 to-purple-600 h-24"></div>

      <div className="max-w-6xl mx-auto px-6 -mt-12 relative">
        {/* Profile Header Card */}
        <Card className="mb-8 shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                {/* Profile Avatar */}
                <div className="w-16 h-16 bg-pink-500 rounded-full flex items-center justify-center text-white font-bold text-xl">
                  {user?.email ? getUserInitials(user.email) : 'U'}
                </div>

                {/* User Info */}
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    {user?.email ? getUserDisplayName(user.email) : 'User'}
                  </h1>
                  <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                    <div className="flex items-center gap-1">
                      <Mail className="w-4 h-4" />
                      {user?.email}
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      Member since {user?.created_at ? formatDate(user.created_at) : 'Recently'}
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2">
                <Button variant="outline" size="sm" className="text-pink-600 border-pink-200 hover:bg-pink-50">
                  <Edit className="w-4 h-4 mr-2" />
                  Edit Profile
                </Button>
                <Button variant="outline" size="sm" onClick={handleSignOut}>
                  <LogOut className="w-4 h-4 mr-2" />
                  Sign Out
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Subscription Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-pink-600">
                <Crown className="w-5 h-5" />
                Subscription
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Plan Info */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-semibold text-lg">
                    {planInfo?.name || 'Basic Plan'}
                  </h3>
                  <Badge variant={isProUser ? "default" : "secondary"} className={isProUser ? "bg-green-100 text-green-800" : ""}>
                    {isProUser ? 'Active' : 'Free'}
                  </Badge>
                </div>
                <p className="text-sm text-gray-600 mb-4">
                  {isProUser ? 'Monthly billing' : 'Free plan with limited features'}
                </p>

                {isProUser && subscription?.next_billing_date && (
                  <p className="text-sm text-gray-600 mb-4">
                    Next billing: {new Date(subscription.next_billing_date).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </p>
                )}
              </div>

              {/* Plan Features */}
              <div>
                <h4 className="font-medium mb-3">Plan Features:</h4>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>Unlimited assessments</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>Advanced analytics</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>Priority support</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>Custom integrations</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>Team collaboration</span>
                  </div>
                </div>
              </div>

              {/* Usage Stats */}
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>API Calls Used</span>
                    <span>{isProUser ? '2400 / 5000' : `${credits} / ${planInfo?.credits || 10}`}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full"
                      style={{ width: isProUser ? '48%' : `${(credits / (planInfo?.credits || 10)) * 100}%` }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Storage Used</span>
                    <span>1200 MB / 5000 MB</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-500 h-2 rounded-full" style={{ width: '24%' }}></div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2">
                {canUpgrade ? (
                  <Button
                    className="flex-1 bg-gradient-to-r from-pink-500 to-purple-600 text-white"
                    onClick={handleUpgradeClick}
                    disabled={loading}
                  >
                    <Crown className="h-4 w-4 mr-2" />
                    Upgrade Plan
                  </Button>
                ) : (
                  <Button variant="outline" className="flex-1" disabled>
                    <Settings className="h-4 w-4 mr-2" />
                    Manage
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Account Settings Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-pink-600">
                <Settings className="w-5 h-5" />
                Account Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Notifications */}
              <div>
                <h3 className="font-medium mb-4">Notifications</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Mail className="w-4 h-4 text-gray-500" />
                      <div>
                        <p className="font-medium">Email Notifications</p>
                        <p className="text-sm text-gray-600">Receive updates via email</p>
                      </div>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <BellRing className="w-4 h-4 text-gray-500" />
                      <div>
                        <p className="font-medium">Push Notifications</p>
                        <p className="text-sm text-gray-600">Get instant notifications</p>
                      </div>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <TrendingUp className="w-4 h-4 text-gray-500" />
                      <div>
                        <p className="font-medium">Marketing Updates</p>
                        <p className="text-sm text-gray-600">Product news and promotions</p>
                      </div>
                    </div>
                    <Switch />
                  </div>
                </div>
              </div>

              {/* Privacy */}
              <div>
                <h3 className="font-medium mb-4">Privacy</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Eye className="w-4 h-4 text-gray-500" />
                      <div>
                        <p className="font-medium">Public Profile</p>
                        <p className="text-sm text-gray-600">Make your profile visible to others</p>
                      </div>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Activity className="w-4 h-4 text-gray-500" />
                      <div>
                        <p className="font-medium">Activity Tracking</p>
                        <p className="text-sm text-gray-600">Track your usage analytics</p>
                      </div>
                    </div>
                    <Switch defaultChecked />
                  </div>
                </div>
              </div>

              {/* Security */}
              <div>
                <h3 className="font-medium mb-4">Security</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Shield className="w-4 h-4 text-gray-500" />
                      <div>
                        <p className="font-medium">Two-Factor Authentication</p>
                        <p className="text-sm text-gray-600">Add an extra layer of security</p>
                      </div>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Lock className="w-4 h-4 text-gray-500" />
                      <div>
                        <p className="font-medium">Active Sessions</p>
                        <p className="text-sm text-gray-600">Monitor your active sessions</p>
                      </div>
                    </div>
                    <Switch defaultChecked />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Upgrade Modal */}
      <Dialog open={showUpgradeModal} onOpenChange={setShowUpgradeModal}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Crown className="h-5 w-5 text-yellow-500" />
              Upgrade to Pro
            </DialogTitle>
            <DialogDescription>
              Unlock more credits and advanced features with Pro plan.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg">
              <h4 className="font-semibold mb-2">Pro Plan Benefits:</h4>
              <ul className="space-y-1 text-sm">
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  5,000 API calls per month
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  Priority support
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  Advanced analytics
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  Custom integrations
                </li>
              </ul>
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold">$9.99</div>
              <div className="text-sm text-gray-600">per month</div>
            </div>
          </div>

          <DialogFooter className="flex gap-2">
            <Button variant="outline" onClick={() => setShowUpgradeModal(false)}>
              Cancel
            </Button>
            <Button
              className="bg-gradient-to-r from-purple-500 to-pink-500 text-white"
              onClick={handleUpgradeClick}
              disabled={upgrading}
            >
              {upgrading ? 'Processing...' : 'Upgrade Now'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Profile;