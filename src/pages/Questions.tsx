import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuestions } from '@/contexts/QuestionsContext';
import { useAuth } from '@/contexts/AuthContext';
import { useSubscription } from '@/hooks/useSubscription';
import Header from '@/components/Header';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, Filter, CheckCircle, Circle, Clock, Trophy, Crown, Lock } from 'lucide-react';
import { listDesignsFromSupabase } from '@/services/designService';
import { getBestScore } from '@/services/bestDesignService';
import { isFeatureEnabled } from '@/config/featureFlags';
import FloatingFeedbackButton from '@/components/FloatingFeedbackButton';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const categoryColors = {
  'system-design': 'bg-blue-100 text-blue-800 border-blue-200',
  'architecture': 'bg-purple-100 text-purple-800 border-purple-200',
  'scalability': 'bg-orange-100 text-orange-800 border-orange-200',
  'database': 'bg-cyan-100 text-cyan-800 border-cyan-200',
  'frontend': 'bg-pink-100 text-pink-800 border-pink-200',
  'backend': 'bg-indigo-100 text-indigo-800 border-indigo-200'
};

// Question status types
type QuestionStatus = 'not-attempted' | 'attempted' | 'solved';

interface QuestionStatusMap {
  [questionId: string]: QuestionStatus;
}

interface QuestionScoreMap {
  [questionId: string]: number | null;
}

const Questions: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { hasActiveSubscription, canAccessQuestion, getAccessibleQuestionIds } = useSubscription();
  const {
    filteredQuestions,
    loading,
    error,
    setCurrentQuestion,
    filterQuestions,
    resetFilters
  } = useQuestions();

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDifficulties, setSelectedDifficulties] = useState<('easy' | 'medium' | 'hard')[]>([]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [questionStatuses, setQuestionStatuses] = useState<QuestionStatusMap>({});
  const [questionScores, setQuestionScores] = useState<QuestionScoreMap>({});
  const [accessibleQuestions, setAccessibleQuestions] = useState<number[]>([]);

  // Load question statuses and scores when component mounts or user changes
  useEffect(() => {
    const loadQuestionData = async () => {
      if (!user) return;

      try {
        // Get all designs for the user
        const designs = await listDesignsFromSupabase(user.id, 'question');
        const statusMap: QuestionStatusMap = {};
        const scoreMap: QuestionScoreMap = {};

        // Load data for each question
        await Promise.all(filteredQuestions.map(async (question) => {
          const hasDesign = designs.some(design => design.questionId === question.id);

          // Get best score for this question
          try {
            const bestScore = await getBestScore(user.id, question.id, 'question', question.id);
            scoreMap[question.id] = bestScore;

            // Determine status based on design existence and score
            if (bestScore !== null && bestScore >= 8) {
              statusMap[question.id] = 'solved'; // Has a high score (8+), so it's solved
            } else if (bestScore !== null || hasDesign) {
              statusMap[question.id] = 'attempted'; // Has design or score but not solved
            } else {
              statusMap[question.id] = 'not-attempted';
            }
          } catch (error) {
            debugError(`Error loading best score for question ${question.id}:`, error);
            scoreMap[question.id] = null;
            statusMap[question.id] = hasDesign ? 'attempted' : 'not-attempted';
          }
        }));

        setQuestionStatuses(statusMap);
        setQuestionScores(scoreMap);
      } catch (error) {
        debugError('Error loading question data:', error);
      }
    };

    loadQuestionData();
  }, [user, filteredQuestions]);

  // Check question accessibility based on subscription status
  useEffect(() => {
    const checkAccessibility = async () => {
      if (!user) return;

      try {
        // Use the more efficient getAccessibleQuestionIds method
        const accessibleIds = await getAccessibleQuestionIds();

        if (accessibleIds.length === 0) {
          // Empty array means access to all questions (pro user)
          setAccessibleQuestions(filteredQuestions.map(q => q.id));
        } else {
          // Specific IDs means limited access (basic user)
          setAccessibleQuestions(accessibleIds);
        }

        debugLog(`User can access questions:`, accessibleIds.length === 0 ? 'all' : accessibleIds);
      } catch (error) {
        debugError('Error checking question accessibility:', error);
        // Default to URL shortener only for basic users
        setAccessibleQuestions([1]);
      }
    };

    checkAccessibility();
  }, [user, filteredQuestions, getAccessibleQuestionIds]);

  // Helper function to get status icon and color
  const getStatusDisplay = (questionId: string) => {
    const status = questionStatuses[questionId] || 'not-attempted';

    switch (status) {
      case 'solved':
        return {
          icon: <CheckCircle className="h-4 w-4" />,
          color: 'text-green-500',
          bgColor: 'bg-green-500/10',
          label: 'Solved',
          showScore: true
        };
      case 'attempted':
        return {
          icon: <Clock className="h-4 w-4" />,
          color: 'text-yellow-500',
          bgColor: 'bg-yellow-500/10',
          label: 'Attempted',
          showScore: false
        };
      default:
        return {
          icon: <Circle className="h-4 w-4" />,
          color: 'text-gray-400',
          bgColor: 'bg-gray-500/10',
          label: 'Not Attempted',
          showScore: false
        };
    }
  };

  // Helper function to render score badge
  const renderScoreBadge = (questionId: string) => {
    const score = questionScores[questionId];
    if (score === null || score === undefined) return null;

    // Determine color based on score
    let colorClass = '';
    if (score >= 8) {
      colorClass = 'bg-green-100 text-green-800 border-green-200';
    } else if (score >= 6) {
      colorClass = 'bg-yellow-100 text-yellow-800 border-yellow-200';
    } else {
      colorClass = 'bg-red-100 text-red-800 border-red-200';
    }

    return (
      <div className={`flex items-center gap-1 px-2 py-1 rounded-full ${colorClass}`}>
        <Trophy className="h-3 w-3" />
        <span className="text-xs font-medium">{score}/10</span>
      </div>
    );
  };



  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    filterQuestions({
      searchTerm,
      difficulty: selectedDifficulties,
      category: selectedCategories
    });
  };

  const handleResetFilters = () => {
    setSearchTerm('');
    setSelectedDifficulties([]);
    setSelectedCategories([]);
    resetFilters();
  };

  const handleQuestionSelect = (questionId: number) => {
    const question = filteredQuestions.find(q => q.id === questionId);
    if (question) {
      setCurrentQuestion(question);
      navigate(`/questions/${questionId}`);
    }
  };

  const handleStartAssessment = () => {
    navigate('/canvas');
  };

  if (loading) {
    return (
      <div className="flex flex-col min-h-screen">
        <Header onStartAssessment={handleStartAssessment} />
        <div className="flex-1 p-6 flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col min-h-screen">
        <Header onStartAssessment={handleStartAssessment} />
        <div className="flex-1 p-6 flex items-center justify-center">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="text-2xl text-center text-red-600">Error</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-center">{error}</p>
            </CardContent>
            <CardFooter className="flex justify-center">
              <Button onClick={handleResetFilters}>Try Again</Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-gray-100">
      <Header onStartAssessment={handleStartAssessment} />

      <div className="flex-1 p-6">
        <div className="max-w-7xl mx-auto">
          {/* Subscription Upgrade Banner for Basic Users */}
          {!hasActiveSubscription && (
            <div className="mb-6 bg-gradient-to-r from-purple-500/10 to-blue-500/10 border border-purple-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Crown className="h-5 w-5 text-purple-600" />
                  <div>
                    <h3 className="font-semibold text-purple-900">Unlock All Questions with Pro</h3>
                    <p className="text-sm text-purple-700">
                      You currently have access to the URL Shortener question. Upgrade to Pro to access all system design questions.
                    </p>
                  </div>
                </div>
                <Button
                  onClick={() => navigate('/profile')}
                  className="bg-gradient-to-r from-purple-500 to-blue-500 text-white hover:from-purple-600 hover:to-blue-600"
                >
                  <Crown className="h-4 w-4 mr-2" />
                  Upgrade to Pro
                </Button>
              </div>
            </div>
          )}

          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold text-gray-900">Design Questions</h1>
            {isFeatureEnabled('FREE_CANVAS') && (
              <Button
                onClick={() => navigate('/canvas')}
                className="bg-gradient-to-r from-blue-500/80 to-indigo-600/80 hover:from-blue-600/90 hover:to-indigo-700/90 text-white border-none backdrop-blur-sm"
              >
                Go to Canvas
              </Button>
            )}
          </div>



          <div className="mb-6">
            <form onSubmit={handleSearch} className="flex gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  type="text"
                  placeholder="Search questions..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className="gap-2"
                  >
                    <Filter className="h-4 w-4" />
                    Difficulty
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel>Filter by Difficulty</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuCheckboxItem
                    checked={selectedDifficulties.includes('easy')}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setSelectedDifficulties([...selectedDifficulties, 'easy']);
                      } else {
                        setSelectedDifficulties(selectedDifficulties.filter(d => d !== 'easy'));
                      }
                    }}
                  >
                    Easy
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={selectedDifficulties.includes('medium')}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setSelectedDifficulties([...selectedDifficulties, 'medium']);
                      } else {
                        setSelectedDifficulties(selectedDifficulties.filter(d => d !== 'medium'));
                      }
                    }}
                  >
                    Medium
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={selectedDifficulties.includes('hard')}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setSelectedDifficulties([...selectedDifficulties, 'hard']);
                      } else {
                        setSelectedDifficulties(selectedDifficulties.filter(d => d !== 'hard'));
                      }
                    }}
                  >
                    Hard
                  </DropdownMenuCheckboxItem>
                </DropdownMenuContent>
              </DropdownMenu>

              <Button
                type="submit"
                className="bg-gradient-to-r from-purple-500/80 to-indigo-600/80 hover:from-purple-600/90 hover:to-indigo-700/90 text-white border-none backdrop-blur-sm"
              >
                Search
              </Button>
              <Button
                type="button"
                variant="ghost"
                onClick={handleResetFilters}
              >
                Reset
              </Button>
            </form>
          </div>

          <Tabs defaultValue="all" className="mb-6">
            <TabsList>
              <TabsTrigger
                value="all"
                className="data-[state=active]:bg-purple-500/80 data-[state=active]:text-white"
              >
                All Questions
              </TabsTrigger>
              <TabsTrigger
                value="easy"
                className="data-[state=active]:bg-green-500/80 data-[state=active]:text-white"
              >
                Easy
              </TabsTrigger>
              <TabsTrigger
                value="medium"
                className="data-[state=active]:bg-yellow-500/80 data-[state=active]:text-white"
              >
                Medium
              </TabsTrigger>
              <TabsTrigger
                value="hard"
                className="data-[state=active]:bg-red-500/80 data-[state=active]:text-white"
              >
                Hard
              </TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="mt-4">
              <div className="space-y-2">
                {filteredQuestions.map((question, index) => {
                  const isAccessible = accessibleQuestions.includes(question.id);
                  const isLocked = !isAccessible;

                  return (
                    <div
                      key={question.id}
                      className={`flex items-center py-4 px-4 border-b border-purple-100 transition-colors backdrop-blur-sm rounded-lg mb-2 ${
                        isLocked
                          ? 'bg-gray-50 opacity-60 cursor-not-allowed'
                          : 'hover:bg-purple-50 cursor-pointer bg-purple-25'
                      }`}
                      onClick={() => isAccessible && handleQuestionSelect(question.id)}
                    >
                      {/* Question Number */}
                      <div className="w-12 text-sm text-purple-700 font-semibold">
                        {index + 1}.
                      </div>

                      {/* Question Title */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <h3 className={`text-base font-semibold transition-colors ${
                            isLocked ? 'text-gray-500' : 'text-gray-900 hover:text-purple-700'
                          }`}>
                            {question.title}
                          </h3>
                          {isLocked && (
                            <div className="flex items-center gap-1">
                              <Lock className="h-4 w-4 text-gray-400" />
                              <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                                Pro Only
                              </span>
                            </div>
                          )}
                        </div>
                        {isLocked && (
                          <p className="text-xs text-gray-500 mt-1">
                            Upgrade to Pro to access this question
                          </p>
                        )}
                      </div>

                    {/* Status, Score, and Difficulty */}
                    <div className="flex items-center gap-3">
                      {/* Best Score Badge */}
                      {user && renderScoreBadge(question.id)}

                      {/* Status Indicator */}
                      {user && (() => {
                        const statusDisplay = getStatusDisplay(question.id);
                        return (
                          <div className={`flex items-center gap-1 px-2 py-1 rounded-full ${statusDisplay.bgColor}`}>
                            <span className={statusDisplay.color}>
                              {statusDisplay.icon}
                            </span>
                            <span className={`text-xs font-medium ${statusDisplay.color}`}>
                              {statusDisplay.label}
                            </span>
                          </div>
                        );
                      })()}

                      {/* Difficulty Badge */}
                      <Badge
                        variant="outline"
                        className={`text-xs font-medium ${
                          question.difficulty === 'easy' ? 'text-green-600 border-green-200' :
                          question.difficulty === 'medium' ? 'text-yellow-600 border-yellow-200' :
                          'text-red-600 border-red-200'
                        }`}
                      >
                        {question.difficulty.charAt(0).toUpperCase() + question.difficulty.slice(1)}
                      </Badge>
                    </div>
                  </div>
                  );
                })}
              </div>

              {filteredQuestions.length === 0 && (
                <div className="text-center py-12">
                  <p className="text-lg text-gray-700">No questions found matching your criteria.</p>
                  <Button
                    className="mt-4 bg-gradient-to-r from-purple-500/80 to-indigo-600/80 hover:from-purple-600/90 hover:to-indigo-700/90 text-white border-none backdrop-blur-sm"
                    onClick={handleResetFilters}
                  >
                    Reset Filters
                  </Button>
                </div>
              )}
            </TabsContent>

            {['easy', 'medium', 'hard'].map((difficulty) => (
              <TabsContent key={difficulty} value={difficulty} className="mt-4">
                <div className="space-y-2">
                  {filteredQuestions
                    .filter(q => q.difficulty === difficulty)
                    .map((question, index) => {
                      const isAccessible = accessibleQuestions.includes(question.id);
                      const isLocked = !isAccessible;

                      return (
                        <div
                          key={question.id}
                          className={`flex items-center py-4 px-4 border-b border-purple-100 transition-colors backdrop-blur-sm rounded-lg mb-2 ${
                            isLocked
                              ? 'bg-gray-50 opacity-60 cursor-not-allowed'
                              : 'hover:bg-purple-50 cursor-pointer bg-purple-25'
                          }`}
                          onClick={() => isAccessible && handleQuestionSelect(question.id)}
                        >
                          {/* Question Number */}
                          <div className="w-12 text-sm text-purple-700 font-semibold">
                            {index + 1}.
                          </div>

                          {/* Question Title */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <h3 className={`text-base font-semibold transition-colors ${
                                isLocked ? 'text-gray-500' : 'text-gray-900 hover:text-purple-700'
                              }`}>
                                {question.title}
                              </h3>
                              {isLocked && (
                                <div className="flex items-center gap-1">
                                  <Lock className="h-4 w-4 text-gray-400" />
                                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                                    Pro Only
                                  </span>
                                </div>
                              )}
                            </div>
                            {isLocked && (
                              <p className="text-xs text-gray-500 mt-1">
                                Upgrade to Pro to access this question
                              </p>
                            )}
                          </div>

                        {/* Status, Score, and Difficulty */}
                        <div className="flex items-center gap-3">
                          {/* Best Score Badge */}
                          {user && renderScoreBadge(question.id)}

                          {/* Status Indicator */}
                          {user && (() => {
                            const statusDisplay = getStatusDisplay(question.id);
                            return (
                              <div className={`flex items-center gap-1 px-2 py-1 rounded-full ${statusDisplay.bgColor}`}>
                                <span className={statusDisplay.color}>
                                  {statusDisplay.icon}
                                </span>
                                <span className={`text-xs font-medium ${statusDisplay.color}`}>
                                  {statusDisplay.label}
                                </span>
                              </div>
                            );
                          })()}

                          {/* Difficulty Badge */}
                          <Badge
                            variant="outline"
                            className={`text-xs font-medium ${
                              question.difficulty === 'easy' ? 'text-green-600 border-green-200' :
                              question.difficulty === 'medium' ? 'text-yellow-600 border-yellow-200' :
                              'text-red-600 border-red-200'
                            }`}
                          >
                            {question.difficulty.charAt(0).toUpperCase() + question.difficulty.slice(1)}
                          </Badge>
                        </div>
                      </div>
                      );
                    })}
                </div>

                {filteredQuestions.filter(q => q.difficulty === difficulty).length === 0 && (
                  <div className="text-center py-12">
                    <p className="text-lg text-gray-700">No {difficulty} questions found matching your criteria.</p>
                    <Button
                      className="mt-4 bg-gradient-to-r from-purple-500/80 to-indigo-600/80 hover:from-purple-600/90 hover:to-indigo-700/90 text-white border-none backdrop-blur-sm"
                      onClick={handleResetFilters}
                    >
                      Reset Filters
                    </Button>
                  </div>
                )}
              </TabsContent>
            ))}
          </Tabs>
        </div>
      </div>

      {/* Floating Feedback Button */}
      <FloatingFeedbackButton />
    </div>
  );
};

export default Questions;
