import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useSubscription } from '@/hooks/useSubscription';
import { useQuestions } from '@/contexts/QuestionsContext';
import { useNavigate } from 'react-router-dom';
import { fetchRecentActivity, getActivityStats, RecentActivity } from '@/services/recentActivityService';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { FooterWithGrid } from '@/components/blocks/footers/footer-with-grid';
import {
  Play,
  CheckCircle,
  Circle,
  Coins,
  Clock,
  ArrowRight,
  Zap,
  User,
  LogOut,
  ChevronDown,
  Trophy
} from 'lucide-react';

const Dashboard: React.FC = () => {
  const { user, signOut } = useAuth();
  const { credits, planInfo, loading: subscriptionLoading, error: subscriptionError } = useSubscription();
  const { questions } = useQuestions();
  const navigate = useNavigate();

  // State for recent activity
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [activityStats, setActivityStats] = useState({
    totalAttempted: 0,
    totalCompleted: 0,
    averageScore: 0,
    streak: 0
  });
  const [activityLoading, setActivityLoading] = useState(true);

  const handleStartPractice = () => {
    navigate('/questions');
  };

  const handleSignOut = async () => {
    await signOut();
    navigate('/');
  };

  const handleProfile = () => {
    navigate('/profile');
  };

  // Load recent activity data
  useEffect(() => {
    const loadActivityData = async () => {
      if (!user?.id) return;

      setActivityLoading(true);
      try {
        const [activities, stats] = await Promise.all([
          fetchRecentActivity(user.id, 3),
          getActivityStats(user.id)
        ]);

        setRecentActivity(activities);
        setActivityStats(stats);
      } catch (error) {
        console.error('Error loading activity data:', error);
      } finally {
        setActivityLoading(false);
      }
    };

    loadActivityData();
  }, [user?.id]);

  const recommendations = [
    {
      title: "Design Instagram",
      difficulty: "Intermediate",
      duration: "45 min"
    },
    {
      title: "Scale Dropbox",
      difficulty: "Advanced", 
      duration: "60 min"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-lg overflow-hidden">
              <img
                src="/Layrs.jpeg"
                alt="Layrs Logo"
                className="w-full h-full object-cover"
              />
            </div>
            <span className="text-xl font-semibold text-gray-900">Layrs</span>
          </div>
          
          <nav className="flex items-center gap-8">
            <button
              onClick={() => navigate('/dashboard')}
              className="text-gray-900 font-medium bg-transparent border-none cursor-pointer"
            >
              Dashboard
            </button>
            <button
              onClick={() => navigate('/questions')}
              className="text-gray-500 hover:text-gray-900 bg-transparent border-none cursor-pointer"
            >
              Practice
            </button>
          </nav>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button className="flex items-center gap-3 hover:bg-gray-50 rounded-lg p-2 transition-colors cursor-pointer bg-transparent border-none">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-medium text-sm">
                    {user?.email?.charAt(0).toUpperCase() || 'U'}
                  </span>
                </div>
                <span className="text-gray-900 font-medium">
                  {user?.email?.split('@')[0] || 'User'}
                </span>
                <ChevronDown className="w-4 h-4 text-gray-500" />
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuItem onClick={handleProfile} className="cursor-pointer">
                <User className="w-4 h-4 mr-2" />
                Profile
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleSignOut} className="cursor-pointer text-red-600 focus:text-red-600">
                <LogOut className="w-4 h-4 mr-2" />
                Sign Out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-6 py-8">
        {/* Welcome Section */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <div className="w-16 h-16 bg-gradient-to-br from-purple-400 to-pink-500 rounded-2xl flex items-center justify-center">
              <span className="text-white font-bold text-xl">
                {user?.email?.charAt(0).toUpperCase() || 'U'}
              </span>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 mb-1">
                Welcome back, {user?.email?.split('@')[0] || 'User'}! 👋
              </h1>
              <p className="text-gray-600">Ready to continue your system design journey?</p>
              <div className="flex items-center gap-2 mt-2">
                <Badge variant="secondary" className="bg-blue-100 text-blue-700 hover:bg-blue-100">
                  Intermediate Level
                </Badge>
              </div>
            </div>
          </div>
          
          <Button 
            onClick={handleStartPractice}
            className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white px-6 py-3 rounded-xl font-medium flex items-center gap-2"
          >
            <Play className="w-4 h-4" />
            Start Practice
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Questions Attempted */}
          <Card className="bg-blue-50 border-blue-100">
            <CardContent className="p-6">
              <div className="flex items-center gap-3 mb-2">
                <CheckCircle className="w-5 h-5 text-blue-600" />
                <span className="text-blue-700 font-medium">Questions Attempted</span>
              </div>
              <div className="mb-2">
                {activityLoading ? (
                  <span className="text-3xl font-bold text-gray-400">Loading...</span>
                ) : (
                  <>
                    <span className="text-3xl font-bold text-gray-900">{activityStats.totalAttempted}</span>
                    <span className="text-gray-600">/{questions.length}</span>
                  </>
                )}
              </div>
              <span className="text-sm text-gray-600">
                {activityLoading
                  ? 'Loading stats...'
                  : activityStats.averageScore > 0
                    ? `Average score: ${activityStats.averageScore}/10`
                    : 'Start practicing to see your progress'
                }
              </span>
            </CardContent>
          </Card>

          {/* Current Streak
          <Card className="bg-orange-50 border-orange-100">
            <CardContent className="p-6">
              <div className="flex items-center gap-3 mb-2">
                <Flame className="w-5 h-5 text-orange-600" />
                <span className="text-orange-700 font-medium">Current Streak</span>
              </div>
              <div className="mb-2">
                <span className="text-3xl font-bold text-gray-900">7 days</span>
              </div>
              <span className="text-sm text-gray-600">Keep it up! 🔥</span>
            </CardContent>
          </Card> */}

          {/* Credits Available */}
          <Card className="bg-green-50 border-green-100">
            <CardContent className="p-6">
              <div className="flex items-center gap-3 mb-2">
                <Coins className="w-5 h-5 text-green-600" />
                <span className="text-green-700 font-medium">Credits Available</span>
              </div>
              <div className="mb-2">
                {subscriptionLoading ? (
                  <span className="text-3xl font-bold text-gray-400">Loading...</span>
                ) : subscriptionError ? (
                  <span className="text-3xl font-bold text-red-500">Error</span>
                ) : (
                  <span className="text-3xl font-bold text-gray-900">
                    {credits?.toLocaleString() || '0'}
                  </span>
                )}
              </div>
              <span className="text-sm text-gray-600">
                {subscriptionError ? 'Unable to load credits' : `${planInfo?.name || 'Basic Plan'} credits`}
              </span>
            </CardContent>
          </Card>
          
          {/* Subscription */}
          <Card className="bg-purple-50 border-purple-100">
            <CardContent className="p-6">
              <div className="flex items-center gap-3 mb-2">
                <Trophy className="w-5 h-5 text-purple-600" />
                <span className="text-purple-700 font-medium">Subscription</span>
              </div>
              <div className="mb-2">
                {subscriptionLoading ? (
                  <span className="text-3xl font-bold text-gray-400">Loading...</span>
                ) : subscriptionError ? (
                  <span className="text-3xl font-bold text-red-500">Error</span>
                ) : (
                  <span className="text-3xl font-bold text-gray-900">
                    {planInfo?.name || 'Basic Plan'}
                  </span>
                )}
              </div>
              <span className="text-sm text-gray-600">
                {subscriptionError
                  ? 'Unable to load plan info'
                  : planInfo?.credits
                    ? `${planInfo.credits} credits included`
                    : 'Limited access'
                }
              </span>
            </CardContent>
          </Card>
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Activity */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-lg font-semibold text-gray-900">Recent Activity</h2>
                <span className="text-sm text-gray-500">Your latest practice sessions</span>
              </div>
              
              <div className="space-y-4">
                {activityLoading ? (
                  <div className="text-center py-8">
                    <span className="text-gray-500">Loading recent activity...</span>
                  </div>
                ) : recentActivity.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-gray-500 mb-2">No recent activity</p>
                    <p className="text-sm text-gray-400">Start practicing to see your progress here</p>
                  </div>
                ) : (
                  recentActivity.map((activity) => (
                    <div
                      key={activity.id}
                      className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                      onClick={() => {
                        if (activity.contextType === 'question') {
                          navigate(`/questions/${activity.questionId}`);
                        } else if (activity.contextType === 'guided') {
                          navigate(`/guided/${activity.questionId}`);
                        } else {
                          navigate('/questions');
                        }
                      }}
                    >
                      <div className="flex items-center gap-3">
                        {activity.status === 'completed' ? (
                          <CheckCircle className="w-5 h-5 text-green-500" />
                        ) : activity.status === 'in-progress' ? (
                          <Clock className="w-5 h-5 text-yellow-500" />
                        ) : (
                          <Circle className="w-5 h-5 text-gray-400" />
                        )}
                        <div>
                          <p className="font-medium text-gray-900">{activity.title}</p>
                          <p className="text-sm text-gray-500">
                            {activity.daysAgo === 0 ? 'Today' :
                             activity.daysAgo === 1 ? '1 day ago' :
                             `${activity.daysAgo} days ago`}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        {activity.score !== undefined ? (
                          <span className="text-lg font-semibold text-green-600">{activity.score}/10</span>
                        ) : (
                          <span className="text-sm text-gray-400">In Progress</span>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>

          {/* Recommended for You */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-2 mb-6">
                <Zap className="w-5 h-5 text-yellow-500" />
                <h2 className="text-lg font-semibold text-gray-900">Recommended for You</h2>
              </div>
              <p className="text-sm text-gray-500 mb-6">Based on your learning progress</p>
              
              <div className="space-y-4">
                {recommendations.map((rec, index) => (
                  <div
                    key={index}
                    className="p-4 border border-gray-200 rounded-lg hover:border-gray-300 cursor-pointer transition-colors"
                    onClick={() => navigate('/questions')}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium text-gray-900">{rec.title}</h3>
                      <ArrowRight className="w-4 h-4 text-gray-400" />
                    </div>
                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <span>{rec.difficulty}</span>
                      <div className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        <span>{rec.duration}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </main>

      {/* Footer */}
      <FooterWithGrid />
    </div>
  );
};

export default Dashboard;
