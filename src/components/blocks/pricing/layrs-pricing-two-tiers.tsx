import { Check } from "lucide-react"
import { motion } from "framer-motion"

// Dedicated styles for pricing buttons
const pricingButtonStyles = {
  proPlan: {
    backgroundColor: '#6366f1',
    color: '#ffffff',
    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
  },
  proPlanHover: {
    backgroundColor: '#5855eb',
    boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
  }
}

const tiers = [
  {
    name: 'Free',
    id: 'tier-free',
    href: '#',
    priceMonthly: '$0',
    description: 'Perfect for getting started with system design fundamentals.',
    features: [
      '1 practice problems',
      '10 credits for chat & assessment',
      'Community access',
      'Email support',
    ],
    featured: false,
  },
  {
    name: 'Pro',
    id: 'tier-pro',
    href: '#',
    priceMonthly: '$15',
    description: 'Everything you need to master system design interviews.',
    features: [
      'Unlimited practice problems',
      'Advanced AI feedback with detailed explanations',
      'Priority support',
      'Company-specific problem sets',
      'Progress tracking and analytics',
    ],
    featured: true,
  },
]

export function LayrsPricingTwoTiers() {
  return (
    <div id="pricing" className="bg-background py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <motion.div
          className="mx-auto max-w-4xl text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-base font-semibold text-[#6366f1] font-inter">Pricing</h2>
          <p className="mt-2 text-balance text-4xl font-bold tracking-tight text-foreground font-inter sm:text-5xl">
            Choose the right plan for{' '}
            <span className="bg-gradient-to-r from-[#6366f1] to-[#8b5cf6] bg-clip-text text-transparent">
              your journey
            </span>
          </p>
        </motion.div>
        <motion.p
          className="mx-auto mt-6 max-w-2xl text-center text-lg font-medium text-muted-foreground font-inter"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
        >
          Start with our free tier and upgrade when you're ready to accelerate your system design mastery.
        </motion.p>
        <div className="isolate mx-auto mt-16 grid max-w-md grid-cols-1 gap-y-8 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-2 lg:gap-x-8">
          {tiers.map((tier, tierIdx) => (
            <motion.div
              key={tier.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 * tierIdx }}
              viewport={{ once: true }}
              className={`relative ring-1 rounded-2xl p-8 xl:p-10 transition-all duration-300 hover:shadow-lg ${
                tier.featured
                  ? 'bg-card lg:z-10 shadow-xl ring-[#6366f1]/20 border-[#6366f1]/30'
                  : 'bg-card ring-border hover:ring-[#6366f1]/20'
              }`}
            >
              {tier.featured && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="inline-flex items-center rounded-full bg-[#6366f1] px-4 py-1.5 text-xs font-medium text-white shadow-sm">
                    Most Popular
                  </span>
                </div>
              )}
              <div className="flex items-center justify-between gap-x-4">
                <h3
                  id={tier.id}
                  className={`text-xl font-bold font-inter ${
                    tier.featured ? 'text-[#6366f1]' : 'text-foreground'
                  }`}
                >
                  {tier.name}
                </h3>
              </div>
              <p className="mt-4 text-sm text-muted-foreground font-inter">{tier.description}</p>
              <p className="mt-6 flex items-baseline gap-x-1">
                <span className="text-4xl font-bold tracking-tight text-foreground font-inter">{tier.priceMonthly}</span>
                <span className="text-sm font-semibold text-muted-foreground font-inter">/month</span>
              </p>
              <motion.a
                href={tier.href}
                aria-describedby={tier.id}
                className={`mt-8 block rounded-lg py-3.5 px-6 text-center text-sm font-semibold font-inter transition-all duration-200 ${
                  tier.featured
                    ? 'focus:ring-2 focus:ring-[#6366f1] focus:ring-offset-2'
                    : 'bg-white text-foreground border border-foreground hover:bg-foreground hover:text-white focus:ring-2 focus:ring-foreground focus:ring-offset-2'
                }`}
                style={tier.featured ? pricingButtonStyles.proPlan : {}}
                whileHover={{
                  scale: 1.02,
                  y: -1,
                  ...(tier.featured ? pricingButtonStyles.proPlanHover : {})
                }}
                whileTap={{ scale: 0.98 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                {tier.name === 'Free' ? 'Get started' : 'Subscribe'}
              </motion.a>
              <ul role="list" className="mt-8 space-y-3 text-sm text-muted-foreground">
                {tier.features.map((feature) => (
                  <motion.li
                    key={feature}
                    className="flex gap-x-3 items-start"
                    initial={{ opacity: 0, x: -10 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.4 }}
                    viewport={{ once: true }}
                  >
                    <Check aria-hidden="true" className={`h-5 w-5 flex-none mt-0.5 ${tier.featured ? 'text-[#6366f1]' : 'text-muted-foreground/60'}`} />
                    <span className="font-inter text-foreground">{feature}</span>
                  </motion.li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  )
}