/// <reference types="https://esm.sh/@supabase/functions-js@1.0.0/edge-runtime.d.ts" />
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
// Define common headers for all responses
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-supabase-auth',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS, PUT, DELETE, PATCH',
  'Access-Control-Max-Age': '86400',
  'Content-Type': 'application/json'
};
// Load environment variables
const { SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, SUPABASE_ANON_KEY } = Deno.env.toObject();
// Initialize Supabase admin client at module level
const supabaseAdmin = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
// Helper function to create standardized responses
function createResponse(data, status = 200) {
  return new Response(JSON.stringify(data), {
    status,
    headers: corsHeaders
  });
}
Deno.serve(async (req)=>{
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    console.log('Handling OPTIONS preflight request');
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }
  if (req.method !== 'POST') {
    console.log(`Method not allowed: ${req.method}`);
    return createResponse({
      error: 'Method not allowed'
    }, 405);
  }
  try {
    // Log the request for debugging
    console.log('Request headers:', Object.fromEntries(req.headers.entries()));
    console.log('Request method:', req.method);
    console.log('Request URL:', req.url);
    // Get user from JWT
    const authHeader = req.headers.get('authorization');
    if (!authHeader) {
      console.log('Missing authorization header');
      return createResponse({
        error: 'Unauthorized'
      }, 401);
    }
    // Create Supabase client for user authentication with the auth header
    const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
      global: {
        headers: { Authorization: authHeader },
      },
    });
    // Verify user authentication using the JWT token from the request
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      console.error('Authentication error:', authError);
      return createResponse({
        error: 'Unauthorized',
        details: authError?.message
      }, 401);
    }
    console.log(`Authenticated user: ${user.id}`);
    
    // Parse request body to get user_id
    const body = await req.json();
    const targetUserId = body.user_id || user.id; // Use provided user_id or fall back to authenticated user
    console.log(`Fetching data for user: ${targetUserId}`);
    // Fetch user's active PRO subscription (basic users won't have subscription records)
    // First get the DodoPayments product ID for pro plan
    const { data: proPlan } = await supabaseAdmin.from('plans').select('dodopayments_plan_id').eq('id', 'pro').single();
    const proProductId = proPlan?.dodopayments_plan_id;

    const { data: subscriptionData, error: subscriptionError } = await supabaseAdmin.from('subscriptions').select(`
        id,
        subscription_id,
        product_id,
        status,
        recurring_pre_tax_amount,
        currency,
        quantity,
        next_billing_date,
        trial_period_days,
        addons,
        customer,
        metadata
      `).eq('user_id', targetUserId).eq('status', 'active').eq('product_id', proProductId).order('created_at', {
      ascending: false
    }).limit(1).single();

    if (subscriptionError && subscriptionError.code !== 'PGRST116') {
      console.error('Subscription query error:', subscriptionError);
      // Continue execution - error might be "no rows returned" which is fine for basic users
    }
    // Fetch user's credit balance
    const { data: creditsData, error: creditsError } = await supabaseAdmin.from('user_credits').select('credits').eq('user_id', targetUserId).single();
    if (creditsError) {
      console.error('Credits query error:', creditsError);
    // This could be more serious as users should always have credits record
    }
    console.log(`Fetched data for user ${targetUserId}:`, {
      hasSubscription: !!subscriptionData,
      credits: creditsData?.credits || 0,
      subscriptionStatus: subscriptionData?.status
    });
    // Get plan information - if no subscription, user is on basic plan
    let planInfo: any = null;
    if (subscriptionData?.product_id) {
      // User has active pro subscription - find plan by DodoPayments product ID
      const { data: planData } = await supabaseAdmin.from('plans').select('id, name, price, period, credits').eq('dodopayments_plan_id', subscriptionData.product_id).single();
      if (planData) {
        planInfo = {
          id: planData.id,
          name: planData.name,
          price: planData.price,
          period: planData.period,
          credits: planData.credits
        };
      }
    } else {
      // User is on basic plan (no subscription record)
      const { data: basicPlan } = await supabaseAdmin.from('plans').select('id, name, price, period, credits').eq('id', 'basic').single();
      if (basicPlan) {
        planInfo = {
          id: basicPlan.id,
          name: basicPlan.name,
          price: basicPlan.price,
          period: basicPlan.period,
          credits: basicPlan.credits
        };
      }
    }
    // Build subscription object - null for basic plan users (which is expected)
    const subscription = subscriptionData ? {
      id: subscriptionData.id,
      subscription_id: subscriptionData.subscription_id,
      product_id: subscriptionData.product_id,
      status: subscriptionData.status,
      recurring_pre_tax_amount: subscriptionData.recurring_pre_tax_amount || 0,
      currency: subscriptionData.currency || 'USD',
      quantity: subscriptionData.quantity || 1,
      next_billing_date: subscriptionData.next_billing_date,
      trial_period_days: subscriptionData.trial_period_days || 0,
      addons: subscriptionData.addons || [],
      customer: subscriptionData.customer || {},
      metadata: subscriptionData.metadata || {}
    } : null;
    // Prepare response
    const response = {
      subscription,
      credits: creditsData?.credits || 0,
      planInfo
    };
    console.log('Successfully returning user data');
    return createResponse(response, 200);
  } catch (error) {
    console.error('get-user-data function error:', error);
    // Create consistent error response
    const errorResponse = {
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    };
    return createResponse(errorResponse, 500);
  }
});

// x7e5CeChsCFpA434.AuE-rK_0oZvMUte7aKncFAb6i3bo7FuDnicMhOrxIka2TGkl